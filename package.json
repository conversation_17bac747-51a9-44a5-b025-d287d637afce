{"name": "ai-chat-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^10.7.0", "@vueuse/integrations": "^13.5.0", "axios": "^1.10.0", "highlight.js": "^11.11.1", "marked": "^16.1.1", "vue": "^3.3.11", "vue-dompurify-html": "^5.3.0", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.15", "@vitejs/plugin-vue": "^4.6.2", "typescript": "^5.8.3", "vite": "^5.0.8", "vue-tsc": "^1.8.25"}}