<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { chatApi, sessionManager } from '@/api'
import type { Message } from '@/api/types'
import MarkdownRenderer from '@/components/MarkdownRenderer.vue'

interface Props {
  sidebarCollapsed: boolean
}

defineProps<Props>()

// 聊天状态
const messages = ref<Message[]>([])
const inputText = ref('')
const isLoading = ref(false)
const chatContainer = ref<HTMLElement>()
const selectedFile = ref<File | null>(null)
const previewUrl = ref<string | null>(null)
const uploadType = ref<'image' | 'video' | null>(null)
const uploadError = ref<string | null>(null)
const sessionId = ref<string>('')
const currentAssistantMessage = ref<Message | null>(null)

// 初始化会话ID
onMounted(() => {
  sessionId.value = sessionManager.getSessionId()
})

// 处理文件上传
const handleFileUpload = async (event: Event, type: 'image' | 'video') => {
  const input = event.target as HTMLInputElement
  if (input.files && input.files.length > 0) {
    const file = input.files[0]
    
    // 检查文件类型
    if (type === 'image' && !file.type.match('image/(jpeg|jpg|png)')) {
      alert('请上传jpg或png格式的图片')
      return
    }
    
    if (type === 'video' && !file.type.match('video/*')) {
      alert('请上传有效的视频文件')
      return
    }
    
    // 检查文件大小 (限制为10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('文件大小不能超过10MB')
      return
    }
    
    selectedFile.value = file
    uploadType.value = type
    uploadError.value = null
    
    // 创建预览URL
    if (previewUrl.value) {
      URL.revokeObjectURL(previewUrl.value)
    }
    
    try {
      // 上传文件到服务器
      // 注意：这里我们使用本地预览，实际项目中应该等待上传完成
      previewUrl.value = URL.createObjectURL(file)
      
      // 异步上传文件
      // 在实际项目中，可以等待上传完成后再允许发送消息
      /* 
      const uploadResult = await chatApi.uploadFile(file)
      previewUrl.value = uploadResult.url
      */
    } catch (error) {
      console.error('文件上传预览失败:', error)
      uploadError.value = '文件预览失败，请重试'
      clearSelectedFile()
    }
  }
}

// 清除选择的文件
const clearSelectedFile = () => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
  selectedFile.value = null
  previewUrl.value = null
  uploadType.value = null
  uploadError.value = null
}

// 处理流式响应
const handleStreamChunk = (chunk: string) => {
  if (currentAssistantMessage.value) {
    // 更新现有消息内容
    currentAssistantMessage.value.content += chunk
    nextTick(() => scrollToBottom())
  }
}

// 发送消息
const sendMessage = async () => {
  if ((!inputText.value.trim() && !selectedFile.value) || isLoading.value) return

  const userMessage: Message = {
    id: Date.now(),
    type: 'user',
    content: inputText.value.trim(),
    timestamp: new Date(),
    mediaType: uploadType.value || 'text',
    mediaUrl: previewUrl.value || undefined
  }

  messages.value.push(userMessage)
  const currentInput = inputText.value
  inputText.value = ''
  isLoading.value = true
  
  // 保存文件引用
  const file = selectedFile.value
  
  // 清除已上传的文件
  clearSelectedFile()

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  try {
    // 创建助手消息占位符
    currentAssistantMessage.value = {
      id: Date.now() + 1,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      mediaType: 'text'
    }
    
    // 添加到消息列表
    messages.value.push(currentAssistantMessage.value)
    
    // 调用API发送消息，使用流式处理
    await chatApi.sendMessage(
      {
        message: currentInput,
        session_id: sessionId.value,
        image_0: file
      },
      handleStreamChunk
    )
    
    // 流处理完成后，currentAssistantMessage已经包含完整内容
    currentAssistantMessage.value = null
  } catch (error) {
    console.error('发送消息失败:', error)
    
    // 显示错误消息
    if (currentAssistantMessage.value) {
      if (currentAssistantMessage.value.content.length === 0) {
        currentAssistantMessage.value.content = '抱歉，发送消息失败，请稍后再试。'
      }
      currentAssistantMessage.value = null
    } else {
      const errorMessage: Message = {
        id: Date.now() + 1,
        type: 'assistant',
        content: '抱歉，发送消息失败，请稍后再试。',
        timestamp: new Date(),
        mediaType: 'text'
      }
      
      messages.value.push(errorMessage)
    }
  } finally {
    isLoading.value = false
    nextTick(() => scrollToBottom())
  }
}

// 创建新会话
const createNewSession = () => {
  sessionId.value = sessionManager.createNewSession()
  messages.value = []
}

// 测试 Markdown 渲染
const testMarkdown = () => {
  const testMessage: Message = {
    id: Date.now(),
    type: 'assistant',
    content: `# Markdown 测试

这是一个 **粗体** 和 *斜体* 的测试。

## 代码块测试

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
  return "测试成功";
}
\`\`\`

## 列表测试

- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

## 表格测试

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

## 引用测试

> 这是一个引用块
> 可以包含多行内容

## 链接测试

这是一个 [链接](https://example.com) 的测试。

行内代码测试：\`console.log("hello")\``,
    timestamp: new Date(),
    mediaType: 'text'
  }

  messages.value.push(testMessage)
  nextTick(() => scrollToBottom())
}

// 滚动到底部
const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

// 处理回车发送
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}
</script>

<template>
  <div class="chat-area">
    <!-- 聊天消息区域 -->
    <div ref="chatContainer" class="messages-container">
      <div v-if="messages.length === 0" class="welcome-message">
        <h3>欢迎使用AI助手</h3>
        <p>请在下方输入您的问题，我将为您提供帮助。</p>
        <button class="test-markdown-btn" @click="testMarkdown">
          测试 Markdown 渲染
        </button>
        <button v-if="messages.length > 0" class="new-session-btn" @click="createNewSession">
          开始新会话
        </button>
      </div>

      <div 
        v-for="message in messages" 
        :key="message.id"
        class="message"
        :class="message.type"
      >
        <div class="message-content">
          <!-- 文本消息 -->
          <div v-if="!message.mediaType || message.mediaType === 'text'" class="message-text">
            <!-- 如果是助手消息，使用 Markdown 渲染 -->
            <MarkdownRenderer
              v-if="message.type === 'assistant'"
              :content="message.content"
              :is-streaming="currentAssistantMessage && currentAssistantMessage.id === message.id"
            />
            <!-- 用户消息保持原样 -->
            <pre v-else class="message-pre">{{ message.content }}</pre>
            <span v-if="currentAssistantMessage && currentAssistantMessage.id === message.id" class="cursor"></span>
          </div>
          
          <!-- 图片消息 -->
          <div v-else-if="message.mediaType === 'image'" class="message-media">
            <img :src="message.mediaUrl" alt="图片消息" class="message-image" />
            <div v-if="message.content" class="message-text">
              <!-- 如果是助手消息，使用 Markdown 渲染 -->
              <MarkdownRenderer
                v-if="message.type === 'assistant'"
                :content="message.content"
                :is-streaming="currentAssistantMessage && currentAssistantMessage.id === message.id"
              />
              <!-- 用户消息保持原样 -->
              <pre v-else class="message-pre">{{ message.content }}</pre>
            </div>
          </div>

          <!-- 视频消息 -->
          <div v-else-if="message.mediaType === 'video'" class="message-media">
            <video :src="message.mediaUrl" controls class="message-video"></video>
            <div v-if="message.content" class="message-text">
              <!-- 如果是助手消息，使用 Markdown 渲染 -->
              <MarkdownRenderer
                v-if="message.type === 'assistant'"
                :content="message.content"
                :is-streaming="currentAssistantMessage && currentAssistantMessage.id === message.id"
              />
              <!-- 用户消息保持原样 -->
              <pre v-else class="message-pre">{{ message.content }}</pre>
            </div>
          </div>
          
          <div class="message-time">
            {{ message.timestamp.toLocaleTimeString() }}
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading && !currentAssistantMessage" class="message assistant">
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <!-- 预览区域 -->
      <div v-if="previewUrl" class="preview-container">
        <div class="preview-header">
          <span>{{ uploadType === 'image' ? '图片预览' : '视频预览' }}</span>
          <button class="clear-preview" @click="clearSelectedFile">×</button>
        </div>
        <img v-if="uploadType === 'image'" :src="previewUrl" class="media-preview" alt="图片预览" />
        <video v-if="uploadType === 'video'" :src="previewUrl" class="media-preview" controls></video>
      </div>
      
      <!-- 上传错误提示 -->
      <div v-if="uploadError" class="upload-error">
        {{ uploadError }}
        <button class="clear-error" @click="uploadError = null">×</button>
      </div>
      
      <div class="input-container">
        <textarea
          v-model="inputText"
          placeholder="请输入您的问题..."
          class="message-input"
          rows="10"
          @keydown="handleKeydown"
        ></textarea>
        
        <div class="action-buttons">
          <input
            type="file"
            id="image-upload"
            accept="image/jpeg,image/jpg,image/png"
            class="file-input"
            @change="(e) => handleFileUpload(e, 'image')"
          />
          <label for="image-upload" class="upload-button">
            上传图片
          </label>
          
          <input
            type="file"
            id="video-upload"
            accept="video/*"
            class="file-input"
            @change="(e) => handleFileUpload(e, 'video')"
          />
          <label for="video-upload" class="upload-button">
            上传视频
          </label>
          
          <button 
            class="send-button"
            :disabled="(!inputText.trim() && !selectedFile) || isLoading"
            @click="sendMessage"
          >
            提交
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #ffffff;
}

.welcome-message {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.welcome-message h3 {
  font-size: 24px;
  margin-bottom: 12px;
  color: #111827;
}

.message {
  margin-bottom: 20px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
}

.message.user .message-content {
  background: #3b82f6;
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background: #f3f4f6;
  color: #111827;
  border-bottom-left-radius: 4px;
}

.message-text {
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-pre {
  margin: 0;
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 4px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* 打字光标动画 */
.cursor {
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: currentColor;
  margin-left: 2px;
  vertical-align: middle;
  animation: cursor-blink 1s step-end infinite;
}

@keyframes cursor-blink {
  from, to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.input-area {
  padding: 20px;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.message-input {
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  outline: none;
  transition: border-color 0.2s;
  width: 100%;
}

.message-input:focus {
  border-color: #3b82f6;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  justify-content: flex-start;
  width: 100%;
  flex-wrap: wrap;
}

.file-input {
  display: none;
}

.upload-button {
  background: #3b82f6;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.2s;
  min-width: 80px;
  text-align: center;
}

.upload-button:hover {
  background: #2563eb;
}

.upload-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.send-button {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  margin-left: auto;
}

.send-button:hover:not(:disabled) {
  background: #2563eb;
}

.send-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.preview-container {
  max-width: 70%;
  margin-bottom: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
}

.preview-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.preview-header span {
  font-size: 14px;
  color: #4b5563;
}

.clear-preview {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 20px;
  padding: 0;
  line-height: 1;
}

.clear-preview:hover {
  color: #6b7280;
}

.media-preview {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.message-media {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  object-fit: contain;
}

.message-video {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
}

.upload-error {
  width: 100%;
  padding: 10px;
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  color: #b91c1c;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.clear-error {
  background: none;
  border: none;
  color: #b91c1c;
  cursor: pointer;
  font-size: 18px;
  padding: 0 8px;
}

.new-session-btn,
.test-markdown-btn {
  margin-top: 16px;
  margin-right: 8px;
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.new-session-btn:hover,
.test-markdown-btn:hover {
  background-color: #2563eb;
}

.test-markdown-btn {
  background-color: #10b981;
}

.test-markdown-btn:hover {
  background-color: #059669;
}
</style>